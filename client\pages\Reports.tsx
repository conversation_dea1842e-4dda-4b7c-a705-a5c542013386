import {
  BarChart3,
  FileText,
  Download,
  Calendar,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Mock data for reports
const monthlyStats = [
  { month: "يناير", licenses: 12, workers: 35, renewals: 8 },
  { month: "فبراير", licenses: 15, workers: 42, renewals: 12 },
  { month: "مارس", licenses: 18, workers: 38, renewals: 15 },
  { month: "أبريل", licenses: 22, workers: 45, renewals: 18 },
  { month: "مايو", licenses: 25, workers: 52, renewals: 22 },
  { month: "يونيو", licenses: 28, workers: 48, renewals: 25 },
];

const licenseTypes = [
  { type: "ترخيص تجاري", count: 45, percentage: 35 },
  { type: "ترخيص صحة", count: 32, percentage: 25 },
  { type: "ترخيص مطافي", count: 28, percentage: 22 },
  { type: "ترخيص إعلان", count: 15, percentage: 12 },
  { type: "تراخيص أخرى", count: 8, percentage: 6 },
];

const workerNationalities = [
  { nationality: "مصري", count: 25, percentage: 28 },
  { nationality: "فلبيني", count: 20, percentage: 22 },
  { nationality: "بنغلاديشي", count: 18, percentage: 20 },
  { nationality: "إثيوبي", count: 15, percentage: 17 },
  { nationality: "أخرى", count: 11, percentage: 13 },
];

const expiryReports = [
  { period: "هذا الشهر", count: 12, type: "تراخيص" },
  { period: "الشهر القادم", count: 8, type: "تراخيص" },
  { period: "خلال 3 أشهر", count: 25, type: "تراخيص" },
  { period: "هذا الشهر", count: 5, type: "جوازات سفر" },
  { period: "الشهر القادم", count: 12, type: "جوازات سفر" },
  { period: "خلال 3 أشهر", count: 28, type: "جوازات سفر" },
];

export function Reports() {
  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold">التقارير والإحصائيات</h1>
          <p className="text-muted-foreground mt-2">
            تحليل شامل للبيانات والأداء
          </p>
        </div>
        <div className="flex gap-2">
          <Select defaultValue="month">
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">هذا الأسبوع</SelectItem>
              <SelectItem value="month">هذا الشهر</SelectItem>
              <SelectItem value="quarter">هذا الربع</SelectItem>
              <SelectItem value="year">هذا العام</SelectItem>
            </SelectContent>
          </Select>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">نمو التراخيص</CardTitle>
            <TrendingUp className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">+15%</div>
            <p className="text-xs text-muted-foreground">
              مقارنة بالشهر الماضي
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل التجديد</CardTitle>
            <BarChart3 className="h-4 w-4 text-info" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">92%</div>
            <p className="text-xs text-muted-foreground">
              من التراخيص المنتهية
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط العمال</CardTitle>
            <TrendingUp className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8.5</div>
            <p className="text-xs text-muted-foreground">عامل لكل عميل</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              الوثائق المنتهية
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">-8%</div>
            <p className="text-xs text-muted-foreground">
              مقارنة بالشهر الماضي
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="licenses">التراخيص</TabsTrigger>
          <TabsTrigger value="workers">العمال</TabsTrigger>
          <TabsTrigger value="expiry">انتهاء الصلاحية</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Monthly Growth Chart */}
            <Card>
              <CardHeader>
                <CardTitle>النمو الشهري</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyStats.slice(-3).map((stat, index) => (
                    <div key={stat.month} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{stat.month}</span>
                        <div className="flex gap-4 text-xs">
                          <span>تراخيص: {stat.licenses}</span>
                          <span>عمال: {stat.workers}</span>
                          <span>تجديدات: {stat.renewals}</span>
                        </div>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className="h-full bg-primary rounded-full transition-all duration-300"
                          style={{ width: `${(stat.licenses / 30) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start gap-2"
                >
                  <FileText className="h-4 w-4" />
                  تقرير العملاء الشهري
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start gap-2"
                >
                  <Calendar className="h-4 w-4" />
                  تقرير انتهاء الصلاحية
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start gap-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  تحليل الأداء السنوي
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start gap-2"
                >
                  <Download className="h-4 w-4" />
                  تصدير جميع البيانات
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="licenses" className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* License Types Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>توزيع أنواع التراخيص</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {licenseTypes.map((license) => (
                    <div key={license.type} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{license.type}</span>
                        <div className="flex items-center gap-2">
                          <span>{license.count}</span>
                          <Badge variant="outline">{license.percentage}%</Badge>
                        </div>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className="h-full bg-primary rounded-full transition-all duration-300"
                          style={{ width: `${license.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* License Status Summary */}
            <Card>
              <CardHeader>
                <CardTitle>ملخص حالة التراخيص</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">تراخيص نشطة</span>
                      <Badge variant="default">124</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">منتهية الصلاحية</span>
                      <Badge variant="destructive">8</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">في انتظار التجديد</span>
                      <Badge variant="secondary">15</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">معلقة</span>
                      <Badge variant="outline">3</Badge>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="text-sm text-muted-foreground">
                      معدل النجاح:{" "}
                      <span className="font-medium text-success">94%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workers" className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Worker Nationalities */}
            <Card>
              <CardHeader>
                <CardTitle>توزيع جنسيات العمال</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {workerNationalities.map((nation) => (
                    <div key={nation.nationality} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">
                          {nation.nationality}
                        </span>
                        <div className="flex items-center gap-2">
                          <span>{nation.count}</span>
                          <Badge variant="outline">{nation.percentage}%</Badge>
                        </div>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className="h-full bg-secondary rounded-full transition-all duration-300"
                          style={{ width: `${nation.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Worker Types */}
            <Card>
              <CardHeader>
                <CardTitle>أنواع العمال</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">عمال</span>
                      <Badge variant="default">52</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">عاملات منزلية</span>
                      <Badge variant="secondary">23</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">التحاق بعائل</span>
                      <Badge variant="outline">14</Badge>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="text-sm text-muted-foreground">
                      إجمالي العمال النشطين:{" "}
                      <span className="font-medium">89</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="expiry" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>تقرير انتهاء الصلاحية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h3 className="font-semibold">التراخيص</h3>
                  {expiryReports
                    .filter((item) => item.type === "تراخيص")
                    .map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <span className="text-sm">{item.period}</span>
                        <Badge
                          variant={
                            item.period === "هذا الشهر"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {item.count} ترخيص
                        </Badge>
                      </div>
                    ))}
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold">جوازات السفر</h3>
                  {expiryReports
                    .filter((item) => item.type === "جوازات سفر")
                    .map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <span className="text-sm">{item.period}</span>
                        <Badge
                          variant={
                            item.period === "هذا الشهر"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {item.count} جواز
                        </Badge>
                      </div>
                    ))}
                </div>
              </div>

              <div className="mt-6 pt-6 border-t">
                <div className="text-center">
                  <Button className="gap-2">
                    <Download className="h-4 w-4" />
                    تصدير تقرير انتهاء الصلاحية
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
