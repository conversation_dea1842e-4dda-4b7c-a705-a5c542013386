import { useLocation, useNavigate } from "react-router-dom";
import {
  Building2,
  CreditCard,
  Users,
  FileText,
  BarChart3,
  X,
  Home,
  Shield,
  UserCheck,
  Clock,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";

interface SidebarProps {
  onClose?: () => void;
}

const navigationItems = [
  {
    title: "الرئيسية",
    href: "/",
    icon: Home,
  },
  {
    title: "العملاء",
    href: "/clients",
    icon: Building2,
  },
  {
    title: "التراخيص والتصاريح",
    href: "/licenses",
    icon: Shield,
  },
  {
    title: "العمال",
    href: "/workers",
    icon: Users,
  },
  {
    title: "التقارير",
    href: "/reports",
    icon: BarChart3,
  },
];

const quickActions = [
  {
    title: "المنتهية الصلاحية",
    href: "/expired",
    icon: Clock,
    badge: "12",
  },
  {
    title: "التصاريح المعلقة",
    href: "/pending",
    icon: UserCheck,
    badge: "5",
  },
];

export function Sidebar({ onClose }: SidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();

  const handleNavigation = (href: string) => {
    navigate(href);
    onClose?.();
  };

  return (
    <div className="flex h-full flex-col bg-card border-l border-border">
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Shield className="h-4 w-4" />
          </div>
          <h2 className="text-lg font-semibold">إدارة التراخيص</h2>
        </div>
        {onClose && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="md:hidden"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <ScrollArea className="flex-1 px-3">
        <div className="space-y-2 py-4">
          {/* Main Navigation */}
          <div className="space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;

              return (
                <Button
                  key={item.href}
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start gap-3 text-right h-10",
                    isActive &&
                      "bg-primary/10 text-primary hover:bg-primary/10",
                  )}
                  onClick={() => handleNavigation(item.href)}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.title}</span>
                </Button>
              );
            })}
          </div>

          <Separator className="my-4" />

          {/* Quick Actions */}
          <div className="space-y-1">
            <div className="px-3 pb-2">
              <h4 className="text-sm font-medium text-muted-foreground">
                إجراءات سريعة
              </h4>
            </div>
            {quickActions.map((item) => {
              const Icon = item.icon;

              return (
                <Button
                  key={item.href}
                  variant="ghost"
                  className="w-full justify-between gap-3 text-right h-10"
                  onClick={() => handleNavigation(item.href)}
                >
                  <div className="flex items-center gap-3">
                    <Icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </div>
                  <span className="rounded-full bg-destructive px-2 py-0.5 text-xs text-destructive-foreground">
                    {item.badge}
                  </span>
                </Button>
              );
            })}
          </div>
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="border-t border-border p-4">
        <div className="text-center text-xs text-muted-foreground">
          نظام إدارة التراخيص والعمال
          <br />
          الإصدار 1.0
        </div>
      </div>
    </div>
  );
}
