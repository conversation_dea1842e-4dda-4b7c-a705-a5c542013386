import { useState } from "react";
import {
  Search,
  Plus,
  Shield,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Mock data
const licenses = [
  {
    id: "1",
    clientName: "شركة الأمل للتجارة",
    type: "commercial",
    licenseNumber: "COM-2024-001",
    issueDate: new Date("2024-01-15"),
    expiryDate: new Date("2024-12-15"),
    status: "active",
    workersCount: 12,
  },
  {
    id: "2",
    clientName: "مط��م الذوق الأصيل",
    type: "health",
    licenseNumber: "HEALTH-2024-002",
    issueDate: new Date("2024-02-01"),
    expiryDate: new Date("2024-02-15"),
    status: "expired",
    workersCount: 5,
  },
  {
    id: "3",
    clientName: "مؤسسة النور للمقاولات",
    type: "fire_dept",
    licenseNumber: "FIRE-2024-003",
    issueDate: new Date("2024-01-20"),
    expiryDate: new Date("2024-02-25"),
    status: "pending_renewal",
    workersCount: 8,
  },
];

const approvals = [
  {
    id: "1",
    clientName: "شركة الأمل للتجارة",
    type: "affairs_signature",
    approvalNumber: "AFF-2024-001",
    issueDate: new Date("2024-01-15"),
    expiryDate: new Date("2024-12-15"),
    status: "active",
  },
  {
    id: "2",
    clientName: "مؤسسة النور للمقاولات",
    type: "traffic_signature",
    approvalNumber: "TRF-2024-002",
    issueDate: new Date("2024-02-01"),
    expiryDate: new Date("2024-02-20"),
    status: "pending",
  },
];

const representativeCards = [
  {
    id: "1",
    clientName: "شركة الأمل للتجارة",
    type: "traffic_rep",
    cardNumber: "TRP-2024-001",
    holderName: "محمد أحمد السالم",
    issueDate: new Date("2024-01-15"),
    expiryDate: new Date("2024-12-15"),
    status: "active",
  },
  {
    id: "2",
    clientName: "مؤسسة النور للمقاولات",
    type: "affairs_rep",
    cardNumber: "ARP-2024-002",
    holderName: "فاطمة علي الزهراني",
    issueDate: new Date("2024-02-01"),
    expiryDate: new Date("2024-02-25"),
    status: "expired",
  },
];

const licenseTypeLabels = {
  commercial: "ترخيص تجاري",
  fire_dept: "ترخيص مطافي",
  health: "ترخيص صحة",
  advertisement: "ترخيص إعلان",
  other: "ترخيص أخرى",
};

const approvalTypeLabels = {
  affairs_signature: "اعتماد توقيع شؤون",
  traffic_signature: "اعتماد توقيع مرور",
  customs_signature: "اعتماد توقيع جمارك",
};

const cardTypeLabels = {
  traffic_rep: "بطاقة مندوب مرور",
  affairs_rep: "بطاقة مندوب شؤون",
  internal_rep: "بطاقة مندوب داخلية",
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "default";
    case "expired":
      return "destructive";
    case "pending_renewal":
    case "pending":
      return "secondary";
    default:
      return "outline";
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case "active":
      return "نشط";
    case "expired":
      return "منتهي الصلاحية";
    case "pending_renewal":
      return "في انتظار التجديد";
    case "pending":
      return "في الانتظار";
    default:
      return status;
  }
};

const getDaysUntilExpiry = (expiryDate: Date) => {
  const today = new Date();
  const diffTime = expiryDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export function Licenses() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold">التراخيص والتصاريح</h1>
          <p className="text-muted-foreground mt-2">
            إدارة جميع التراخيص والتصاريح والبطاقات
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          إضافة ترخيص جديد
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي التراخيص
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{licenses.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">نشطة</CardTitle>
            <CheckCircle className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">
              {licenses.filter((l) => l.status === "active").length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              منتهية الصلاحية
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {licenses.filter((l) => l.status === "expired").length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              في انتظار التجديد
            </CardTitle>
            <Clock className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {licenses.filter((l) => l.status === "pending_renewal").length}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="licenses" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="licenses">التراخيص</TabsTrigger>
          <TabsTrigger value="approvals">الاعتمادات</TabsTrigger>
          <TabsTrigger value="cards">بطاقات المندوبين</TabsTrigger>
        </TabsList>

        <TabsContent value="licenses" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <CardTitle>قائمة التراخيص</CardTitle>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="فلترة حسب النوع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع التراخيص</SelectItem>
                      <SelectItem value="commercial">ترخيص تجاري</SelectItem>
                      <SelectItem value="health">ترخيص صحة</SelectItem>
                      <SelectItem value="fire_dept">ترخيص مطافي</SelectItem>
                    </SelectContent>
                  </Select>
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="البحث في التراخيص..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pr-10 w-80"
                      dir="rtl"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {licenses.map((license) => {
                  const daysUntilExpiry = getDaysUntilExpiry(
                    license.expiryDate,
                  );

                  return (
                    <div
                      key={license.id}
                      className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">
                              {
                                licenseTypeLabels[
                                  license.type as keyof typeof licenseTypeLabels
                                ]
                              }
                            </h3>
                            <Badge variant={getStatusColor(license.status)}>
                              {getStatusLabel(license.status)}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {license.clientName}
                          </p>
                          <div className="flex items-center gap-4 text-sm">
                            <span>رقم الترخيص: {license.licenseNumber}</span>
                            <span>عدد العمال: {license.workersCount}</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>
                              تاريخ الإصدار:{" "}
                              {license.issueDate.toLocaleDateString("ar-SA")}
                            </span>
                            <span>
                              تاريخ الانتهاء:{" "}
                              {license.expiryDate.toLocaleDateString("ar-SA")}
                            </span>
                          </div>
                          {daysUntilExpiry <= 30 && daysUntilExpiry > 0 && (
                            <div className="flex items-center gap-1 text-warning">
                              <Calendar className="h-4 w-4" />
                              <span className="text-sm">
                                ينتهي خلال {daysUntilExpiry} يوم
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            عرض
                          </Button>
                          <Button size="sm">تجديد</Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approvals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>اعتمادات التوقيع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {approvals.map((approval) => (
                  <div
                    key={approval.id}
                    className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">
                            {
                              approvalTypeLabels[
                                approval.type as keyof typeof approvalTypeLabels
                              ]
                            }
                          </h3>
                          <Badge variant={getStatusColor(approval.status)}>
                            {getStatusLabel(approval.status)}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {approval.clientName}
                        </p>
                        <div className="flex items-center gap-4 text-sm">
                          <span>رقم الاعتماد: {approval.approvalNumber}</span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>
                            تاريخ الإصدار:{" "}
                            {approval.issueDate.toLocaleDateString("ar-SA")}
                          </span>
                          <span>
                            تاريخ الانتهاء:{" "}
                            {approval.expiryDate.toLocaleDateString("ar-SA")}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          عرض
                        </Button>
                        <Button size="sm">تجديد</Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cards" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>بطاقات المندوبين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {representativeCards.map((card) => (
                  <div
                    key={card.id}
                    className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">
                            {
                              cardTypeLabels[
                                card.type as keyof typeof cardTypeLabels
                              ]
                            }
                          </h3>
                          <Badge variant={getStatusColor(card.status)}>
                            {getStatusLabel(card.status)}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {card.clientName}
                        </p>
                        <div className="flex items-center gap-4 text-sm">
                          <span>رقم البطاقة: {card.cardNumber}</span>
                          <span>حامل البطاقة: {card.holderName}</span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>
                            تاريخ الإصدار:{" "}
                            {card.issueDate.toLocaleDateString("ar-SA")}
                          </span>
                          <span>
                            تاريخ الانتهاء:{" "}
                            {card.expiryDate.toLocaleDateString("ar-SA")}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          عرض
                        </Button>
                        <Button size="sm">تجديد</Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
