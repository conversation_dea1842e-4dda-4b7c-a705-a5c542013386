import { AlertTriangle, Calendar, FileText } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// Mock expired items data
const expiredItems = [
  {
    id: "1",
    type: "license",
    clientName: "شركة الأمل للتجارة",
    itemName: "ترخيص صحي",
    expiryDate: new Date("2024-01-20"),
    daysOverdue: 15,
  },
  {
    id: "2",
    type: "passport",
    clientName: "مؤسسة النور",
    itemName: "جواز سفر - أحمد محمد",
    expiryDate: new Date("2024-01-25"),
    daysOverdue: 10,
  },
  {
    id: "3",
    type: "residency",
    clientName: "ع��ئلة السالم",
    itemName: "إقامة - فاطمة علي",
    expiryDate: new Date("2024-02-01"),
    daysOverdue: 3,
  },
];

export function Expired() {
  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-destructive">
          المنتهية الصلاحية
        </h1>
        <p className="text-muted-foreground mt-2">
          جميع التراخيص والوثائق المنتهية الصلاحية التي تحتاج لمتابعة فورية
        </p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="expiry-danger">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المنتهية
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {expiredItems.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              ت��اخيص منتهية
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {expiredItems.filter((item) => item.type === "license").length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              وثائق شخصية منتهية
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {expiredItems.filter((item) => item.type !== "license").length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Expired Items List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-destructive">
            قائمة المنتهية الصلاحية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {expiredItems.map((item) => (
              <div
                key={item.id}
                className="p-4 border border-destructive/20 rounded-lg bg-destructive/5"
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-destructive" />
                      <h3 className="font-semibold text-destructive">
                        {item.itemName}
                      </h3>
                      <Badge variant="destructive">
                        متأخر {item.daysOverdue} يوم
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {item.clientName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      انتهت الصلاحية:{" "}
                      {item.expiryDate.toLocaleDateString("ar-SA")}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      عرض التفاصيل
                    </Button>
                    <Button size="sm" variant="destructive">
                      تجديد فوري
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {expiredItems.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <AlertTriangle className="h-12 w-12 mx-auto mb-2 text-success" />
              <p>ممتاز! لا توجد وثائق منتهية الصلاحية</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
