import { useState } from "react";
import {
  Search,
  Plus,
  Users,
  FileCheck,
  Calendar,
  AlertTriangle,
  User,
  FileText,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Mock data
const workers = [
  {
    id: "1",
    name: "محمد أحمد علي",
    nationality: "مصري",
    clientName: "شركة الأمل للتجارة",
    position: "سائق",
    workerType: "employee",
    passportNumber: "********",
    passportExpiryDate: new Date("2025-06-15"),
    residencyExpiryDate: new Date("2024-03-20"),
    drivingLicenseNumber: "DL789456",
    drivingLicenseExpiryDate: new Date("2024-04-10"),
    isActive: true,
  },
  {
    id: "2",
    name: "فاطمة سالم محمد",
    nationality: "فلبينية",
    clientName: "عائلة الأحمد",
    position: "عاملة منزلية",
    workerType: "domestic",
    passportNumber: "********",
    passportExpiryDate: new Date("2024-02-25"),
    residencyExpiryDate: new Date("2024-05-15"),
    drivingLicenseNumber: "",
    drivingLicenseExpiryDate: null,
    isActive: true,
  },
  {
    id: "3",
    name: "أحمد حسن محمود",
    nationality: "بنغلاديشي",
    clientName: "مؤسسة النور للمقاولات",
    position: "عامل بناء",
    workerType: "employee",
    passportNumber: "********",
    passportExpiryDate: new Date("2025-12-01"),
    residencyExpiryDate: new Date("2024-02-28"),
    drivingLicenseNumber: "",
    drivingLicenseExpiryDate: null,
    isActive: true,
  },
  {
    id: "4",
    name: "مريم علي أحمد",
    nationality: "إثيوبية",
    clientName: "عائلة السالم",
    position: "مربية أطفال",
    workerType: "family_attachment",
    passportNumber: "********",
    passportExpiryDate: new Date("2024-08-15"),
    residencyExpiryDate: new Date("2024-09-20"),
    drivingLicenseNumber: "",
    drivingLicenseExpiryDate: null,
    isActive: true,
  },
];

const workerTypeLabels = {
  employee: "عامل",
  domestic: "عاملة منزلية",
  family_attachment: "التحاق بعائل",
};

const getDaysUntilExpiry = (expiryDate: Date | null) => {
  if (!expiryDate) return null;
  const today = new Date();
  const diffTime = expiryDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

const getExpiryStatus = (days: number | null) => {
  if (days === null) return "none";
  if (days < 0) return "expired";
  if (days <= 30) return "warning";
  return "valid";
};

const getExpiryBadge = (days: number | null) => {
  const status = getExpiryStatus(days);

  switch (status) {
    case "expired":
      return <Badge variant="destructive">منتهي الصلاحية</Badge>;
    case "warning":
      return <Badge variant="secondary">ينتهي قريباً</Badge>;
    case "valid":
      return <Badge variant="default">صالح</Badge>;
    default:
      return <Badge variant="outline">غير محدد</Badge>;
  }
};

export function Workers() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");

  const filteredWorkers = workers.filter((worker) => {
    const matchesSearch =
      worker.name.includes(searchQuery) ||
      worker.clientName.includes(searchQuery) ||
      worker.nationality.includes(searchQuery);

    const matchesType =
      filterType === "all" || worker.workerType === filterType;

    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "active" && worker.isActive) ||
      (filterStatus === "inactive" && !worker.isActive);

    return matchesSearch && matchesType && matchesStatus;
  });

  // Calculate expiry statistics
  const passportExpiring = workers.filter((w) => {
    const days = getDaysUntilExpiry(w.passportExpiryDate);
    return days !== null && days <= 30 && days >= 0;
  }).length;

  const residencyExpiring = workers.filter((w) => {
    const days = getDaysUntilExpiry(w.residencyExpiryDate);
    return days !== null && days <= 30 && days >= 0;
  }).length;

  const licenseExpiring = workers.filter((w) => {
    const days = getDaysUntilExpiry(w.drivingLicenseExpiryDate);
    return days !== null && days <= 30 && days >= 0;
  }).length;

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold">العمال</h1>
          <p className="text-muted-foreground mt-2">
            إدارة بيانات العمال ووثائقهم
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          إضافة عامل جديد
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي العمال</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workers.length}</div>
          </CardContent>
        </Card>

        <Card className="expiry-warning">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              جوازات سفر تنتهي
            </CardTitle>
            <FileCheck className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {passportExpiring}
            </div>
            <p className="text-xs text-muted-foreground">خلال 30 يوم</p>
          </CardContent>
        </Card>

        <Card className="expiry-warning">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إقامات تنتهي</CardTitle>
            <FileText className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {residencyExpiring}
            </div>
            <p className="text-xs text-muted-foreground">خلال 30 يوم</p>
          </CardContent>
        </Card>

        <Card className="expiry-warning">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              رخص قيادة تنتهي
            </CardTitle>
            <Calendar className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {licenseExpiring}
            </div>
            <p className="text-xs text-muted-foreground">خلال 30 يوم</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">قائمة العمال</TabsTrigger>
          <TabsTrigger value="expiry">تنبيهات انتهاء الصلاحية</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <CardTitle>قائمة العمال</CardTitle>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="فلترة حسب النوع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الأنواع</SelectItem>
                      <SelectItem value="employee">عامل</SelectItem>
                      <SelectItem value="domestic">عاملة منزلية</SelectItem>
                      <SelectItem value="family_attachment">
                        التحاق بعائل
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="البحث في العمال..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pr-10 w-80"
                      dir="rtl"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-right">اسم العامل</TableHead>
                      <TableHead className="text-right">الجنسية</TableHead>
                      <TableHead className="text-right">
                        العميل/الشركة
                      </TableHead>
                      <TableHead className="text-right">المنصب</TableHead>
                      <TableHead className="text-right">النوع</TableHead>
                      <TableHead className="text-center">جواز السفر</TableHead>
                      <TableHead className="text-center">الإقامة</TableHead>
                      <TableHead className="text-center">
                        رخصة القيادة
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredWorkers.map((worker) => {
                      const passportDays = getDaysUntilExpiry(
                        worker.passportExpiryDate,
                      );
                      const residencyDays = getDaysUntilExpiry(
                        worker.residencyExpiryDate,
                      );
                      const licenseDays = getDaysUntilExpiry(
                        worker.drivingLicenseExpiryDate,
                      );

                      return (
                        <TableRow key={worker.id}>
                          <TableCell className="font-medium">
                            {worker.name}
                          </TableCell>
                          <TableCell>{worker.nationality}</TableCell>
                          <TableCell>{worker.clientName}</TableCell>
                          <TableCell>{worker.position}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {
                                workerTypeLabels[
                                  worker.workerType as keyof typeof workerTypeLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="space-y-1">
                              {getExpiryBadge(passportDays)}
                              {passportDays !== null && (
                                <div className="text-xs text-muted-foreground">
                                  {passportDays < 0
                                    ? `منتهي منذ ${Math.abs(passportDays)} يوم`
                                    : passportDays === 0
                                      ? "ينتهي اليوم"
                                      : `${passportDays} يوم`}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="space-y-1">
                              {getExpiryBadge(residencyDays)}
                              {residencyDays !== null && (
                                <div className="text-xs text-muted-foreground">
                                  {residencyDays < 0
                                    ? `منتهي منذ ${Math.abs(residencyDays)} يوم`
                                    : residencyDays === 0
                                      ? "ينتهي اليوم"
                                      : `${residencyDays} يوم`}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="space-y-1">
                              {getExpiryBadge(licenseDays)}
                              {licenseDays !== null && (
                                <div className="text-xs text-muted-foreground">
                                  {licenseDays < 0
                                    ? `منتهي منذ ${Math.abs(licenseDays)} يوم`
                                    : licenseDays === 0
                                      ? "ينتهي اليوم"
                                      : `${licenseDays} يوم`}
                                </div>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {filteredWorkers.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-2" />
                  <p>لا توجد عمال مطابقة للبحث</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expiry" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-warning" />
                تنبيهات انتهاء صلاحية الوثائق
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Passport Expiry */}
              <div className="space-y-3">
                <h3 className="font-semibold text-destructive">
                  جوازات السفر المنتهية أو التي تنتهي قريباً
                </h3>
                <div className="space-y-2">
                  {workers
                    .filter((w) => {
                      const days = getDaysUntilExpiry(w.passportExpiryDate);
                      return days !== null && days <= 30;
                    })
                    .map((worker) => {
                      const days = getDaysUntilExpiry(
                        worker.passportExpiryDate,
                      );
                      return (
                        <div
                          key={`passport-${worker.id}`}
                          className="p-3 border rounded-lg bg-destructive/5"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">{worker.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {worker.clientName}
                              </p>
                              <p className="text-sm">
                                رقم الجواز: {worker.passportNumber}
                              </p>
                            </div>
                            <div className="text-right">
                              <Badge variant="destructive">
                                {days! < 0
                                  ? `منتهي منذ ${Math.abs(days!)} يوم`
                                  : days === 0
                                    ? "ينتهي اليوم"
                                    : `${days} يوم متبقي`}
                              </Badge>
                              <p className="text-xs text-muted-foreground mt-1">
                                {worker.passportExpiryDate.toLocaleDateString(
                                  "ar-SA",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>

              {/* Residency Expiry */}
              <div className="space-y-3">
                <h3 className="font-semibold text-warning">
                  الإقامات المنتهية أو التي تنتهي قريباً
                </h3>
                <div className="space-y-2">
                  {workers
                    .filter((w) => {
                      const days = getDaysUntilExpiry(w.residencyExpiryDate);
                      return days !== null && days <= 30;
                    })
                    .map((worker) => {
                      const days = getDaysUntilExpiry(
                        worker.residencyExpiryDate,
                      );
                      return (
                        <div
                          key={`residency-${worker.id}`}
                          className="p-3 border rounded-lg bg-warning/5"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">{worker.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {worker.clientName}
                              </p>
                            </div>
                            <div className="text-right">
                              <Badge variant="secondary">
                                {days! < 0
                                  ? `منتهي منذ ${Math.abs(days!)} يوم`
                                  : days === 0
                                    ? "ينتهي اليوم"
                                    : `${days} يوم متبقي`}
                              </Badge>
                              <p className="text-xs text-muted-foreground mt-1">
                                {worker.residencyExpiryDate?.toLocaleDateString(
                                  "ar-SA",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>

              {/* Driving License Expiry */}
              <div className="space-y-3">
                <h3 className="font-semibold text-info">
                  رخص القيادة المنتهية أو التي تنتهي قريباً
                </h3>
                <div className="space-y-2">
                  {workers
                    .filter((w) => {
                      const days = getDaysUntilExpiry(
                        w.drivingLicenseExpiryDate,
                      );
                      return days !== null && days <= 30;
                    })
                    .map((worker) => {
                      const days = getDaysUntilExpiry(
                        worker.drivingLicenseExpiryDate,
                      );
                      return (
                        <div
                          key={`license-${worker.id}`}
                          className="p-3 border rounded-lg bg-info/5"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">{worker.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {worker.clientName}
                              </p>
                              <p className="text-sm">
                                رقم الرخصة: {worker.drivingLicenseNumber}
                              </p>
                            </div>
                            <div className="text-right">
                              <Badge variant="default">
                                {days! < 0
                                  ? `منتهي منذ ${Math.abs(days!)} يوم`
                                  : days === 0
                                    ? "ينتهي اليوم"
                                    : `${days} يوم متبقي`}
                              </Badge>
                              <p className="text-xs text-muted-foreground mt-1">
                                {worker.drivingLicenseExpiryDate?.toLocaleDateString(
                                  "ar-SA",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
