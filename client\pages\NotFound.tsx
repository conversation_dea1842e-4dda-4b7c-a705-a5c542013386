import { useNavigate } from "react-router-dom";
import { FileX, Home, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export function NotFound() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6 text-center space-y-6">
          <div className="space-y-4">
            <FileX className="h-16 w-16 mx-auto text-muted-foreground" />
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">الصفحة غير موجودة</h1>
              <p className="text-muted-foreground">
                عذراً، الصفحة التي تبحث عنها غير متوفرة أو قد تم نقلها
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <Button onClick={() => navigate("/")} className="w-full gap-2">
              <Home className="h-4 w-4" />
              العودة للرئيسية
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate(-1)}
              className="w-full gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              العودة للخلف
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
