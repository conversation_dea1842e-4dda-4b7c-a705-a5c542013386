import {
  Alert<PERSON>riangle,
  Building2,
  CreditCard,
  Users,
  Calendar,
  CheckCircle,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

// Mock data - in real app this would come from API
const stats = {
  totalClients: 45,
  totalLicenses: 127,
  totalWorkers: 89,
  expiringThisMonth: 12,
  expiredItems: 3,
};

const expiryAlerts = [
  {
    id: "1",
    type: "license",
    clientName: "شركة الأمل للتجارة",
    itemName: "ترخيص تجاري",
    expiryDate: new Date("2024-02-15"),
    daysUntilExpiry: 5,
    severity: "danger" as const,
  },
  {
    id: "2",
    type: "passport",
    clientName: "مؤسسة النور",
    itemName: "جواز سفر - محمد أحمد",
    expiryDate: new Date("2024-02-25"),
    daysUntilExpiry: 15,
    severity: "warning" as const,
  },
  {
    id: "3",
    type: "card",
    clientName: "شركة البناء المتطور",
    itemName: "بطاقة مندوب مرور",
    expiryDate: new Date("2024-03-01"),
    daysUntilExpiry: 20,
    severity: "info" as const,
  },
];

const recentActivity = [
  {
    id: "1",
    action: "تم تجديد ترخيص صحي",
    client: "مطعم الذوق الأصيل",
    time: "منذ ساعتين",
  },
  {
    id: "2",
    action: "تم إضافة عامل جديد",
    client: "شركة المستقبل للإنشاءات",
    time: "منذ 4 ساعات",
  },
  {
    id: "3",
    action: "تم تحديث بيانات العميل",
    client: "مؤسسة التقنية الحديثة",
    time: "أمس",
  },
];

export function Dashboard() {
  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">لوحة التحكم</h1>
        <p className="text-muted-foreground mt-2">
          نظرة عامة على التراخيص والعمال
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="card-gradient">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي العملاء
            </CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClients}</div>
            <p className="text-xs text-muted-foreground">+2 من الشهر الماضي</p>
          </CardContent>
        </Card>

        <Card className="card-gradient">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي التراخيص
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLicenses}</div>
            <p className="text-xs text-muted-foreground">+8 من الشهر الماضي</p>
          </CardContent>
        </Card>

        <Card className="card-gradient">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي العمال</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWorkers}</div>
            <p className="text-xs text-muted-foreground">+5 من الشهر الماضي</p>
          </CardContent>
        </Card>

        <Card className="expiry-warning">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              ينتهي هذا الشهر
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {stats.expiringThisMonth}
            </div>
            <p className="text-xs text-muted-foreground">يتطلب المتابعة</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Expiry Alerts */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                تنبيهات انتهاء الصلاحية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {expiryAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className="flex items-center justify-between p-3 rounded-lg border"
                >
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          alert.severity === "danger"
                            ? "destructive"
                            : alert.severity === "warning"
                              ? "secondary"
                              : "default"
                        }
                      >
                        {alert.daysUntilExpiry} يوم
                      </Badge>
                      <span className="font-medium">{alert.itemName}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {alert.clientName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      ينتهي: {alert.expiryDate.toLocaleDateString("ar-SA")}
                    </p>
                  </div>
                  <Button size="sm" variant="outline">
                    تجديد
                  </Button>
                </div>
              ))}

              {expiryAlerts.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="h-12 w-12 mx-auto mb-2 text-success" />
                  <p>لا توجد تنبيهات انتهاء صلاحية</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>النشاطات الأخيرة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={activity.id}>
                  <div className="space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {activity.action}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {activity.client}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.time}
                    </p>
                  </div>
                  {index < recentActivity.length - 1 && (
                    <Separator className="mt-4" />
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>إحصائيات سريعة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>تراخيص نشطة</span>
                  <span className="font-medium">
                    {stats.totalLicenses - stats.expiredItems}
                  </span>
                </div>
                <Progress
                  value={
                    ((stats.totalLicenses - stats.expiredItems) /
                      stats.totalLicenses) *
                    100
                  }
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>عمال نشطون</span>
                  <span className="font-medium">{stats.totalWorkers}</span>
                </div>
                <Progress value={95} />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>معدل التجديد</span>
                  <span className="font-medium">88%</span>
                </div>
                <Progress value={88} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
