import { Clock, User<PERSON>heck, CheckCircle, XCircle } from "lucide-react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// Mock pending items data
const pendingItems = [
  {
    id: "1",
    type: "approval",
    clientName: "شركة المستقبل للإنشاءات",
    itemName: "اعتماد توقيع مرور",
    submittedDate: new Date("2024-02-01"),
    daysPending: 5,
    priority: "high",
  },
  {
    id: "2",
    type: "renewal",
    clientName: "مطعم الذوق الأصيل",
    itemName: "تجديد ترخيص صحي",
    submittedDate: new Date("2024-02-03"),
    daysPending: 3,
    priority: "medium",
  },
  {
    id: "3",
    type: "card",
    clientName: "مؤسسة النور للمقاولات",
    itemName: "بطا��ة مندوب شؤون",
    submittedDate: new Date("2024-02-05"),
    daysPending: 1,
    priority: "low",
  },
  {
    id: "4",
    type: "license",
    clientName: "شركة التقنية الحديثة",
    itemName: "ترخيص إعلان جديد",
    submittedDate: new Date("2024-02-04"),
    daysPending: 2,
    priority: "high",
  },
  {
    id: "5",
    type: "approval",
    clientName: "مؤسسة البناء المتطور",
    itemName: "اعتماد توقيع جمارك",
    submittedDate: new Date("2024-02-02"),
    daysPending: 4,
    priority: "medium",
  },
];

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "high":
      return "destructive";
    case "medium":
      return "secondary";
    case "low":
      return "outline";
    default:
      return "outline";
  }
};

const getPriorityLabel = (priority: string) => {
  switch (priority) {
    case "high":
      return "عالية";
    case "medium":
      return "متوسطة";
    case "low":
      return "منخفضة";
    default:
      return priority;
  }
};

export function Pending() {
  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-warning">التصاريح المعلقة</h1>
        <p className="text-muted-foreground mt-2">
          جميع الطلبات والتصاريح في انتظار المراجعة والموافقة
        </p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="expiry-warning">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المعلقة
            </CardTitle>
            <Clock className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {pendingItems.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أولوية عالية</CardTitle>
            <UserCheck className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {pendingItems.filter((item) => item.priority === "high").length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أولوية متوسطة</CardTitle>
            <UserCheck className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">
              {pendingItems.filter((item) => item.priority === "medium").length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أولوية منخفضة</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pendingItems.filter((item) => item.priority === "low").length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pending Items List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-warning">قائمة التصاريح المعلقة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pendingItems
              .sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                return (
                  priorityOrder[b.priority as keyof typeof priorityOrder] -
                  priorityOrder[a.priority as keyof typeof priorityOrder]
                );
              })
              .map((item) => (
                <div
                  key={item.id}
                  className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-warning" />
                        <h3 className="font-semibold">{item.itemName}</h3>
                        <Badge variant={getPriorityColor(item.priority)}>
                          أولوية {getPriorityLabel(item.priority)}
                        </Badge>
                        <Badge variant="outline">
                          {item.daysPending} يوم في الانتظار
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {item.clientName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        تاريخ التقديم:{" "}
                        {item.submittedDate.toLocaleDateString("ar-SA")}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        عرض التفاصيل
                      </Button>
                      <Button size="sm" className="gap-2">
                        <CheckCircle className="h-3 w-3" />
                        موافقة
                      </Button>
                      <Button size="sm" variant="destructive" className="gap-2">
                        <XCircle className="h-3 w-3" />
                        رفض
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
          </div>

          {pendingItems.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-2 text-success" />
              <p>ممتاز! لا توجد تصاريح معلقة</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
