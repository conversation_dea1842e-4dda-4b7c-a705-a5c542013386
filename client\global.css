@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;

    --success: 142 71% 45%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;

    --info: 221 83% 53%;
    --info-foreground: 0 0% 98%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217 91% 60%;

    --success: 142 71% 45%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;

    --info: 221 83% 53%;
    --info-foreground: 0 0% 98%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    direction: rtl;
  }

  [dir="ltr"] {
    direction: ltr;
  }

  /* Arabic typography improvements */
  .text-arabic {
    font-family: "Cairo", "Tajawal", "Noto Sans Arabic", sans-serif;
    line-height: 1.8;
  }

  /* Custom scrollbar for RTL */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }
}

@layer components {
  .card-gradient {
    background: linear-gradient(
      135deg,
      hsl(var(--card)) 0%,
      hsl(var(--muted)) 100%
    );
  }

  .expiry-warning {
    background: linear-gradient(
      135deg,
      hsl(var(--warning) / 0.1) 0%,
      hsl(var(--warning) / 0.05) 100%
    );
    border: 1px solid hsl(var(--warning) / 0.2);
  }

  .expiry-danger {
    background: linear-gradient(
      135deg,
      hsl(var(--destructive) / 0.1) 0%,
      hsl(var(--destructive) / 0.05) 100%
    );
    border: 1px solid hsl(var(--destructive) / 0.2);
  }
}

@layer utilities {
  .rtl-flip {
    transform: scaleX(-1);
  }

  .ltr-content {
    direction: ltr;
    text-align: left;
  }
}
