import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { Layout } from "@/components/Layout";
import { Dashboard } from "@/pages/Dashboard";
import { Clients } from "@/pages/Clients";
import { Licenses } from "@/pages/Licenses";
import { Workers } from "@/pages/Workers";
import { Reports } from "@/pages/Reports";
import { Expired } from "@/pages/Expired";
import { Pending } from "@/pages/Pending";
import { NotFound } from "@/pages/NotFound";
import "./global.css";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light">
        <div className="min-h-screen bg-background font-arabic" dir="rtl">
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Layout />}>
                <Route index element={<Dashboard />} />
                <Route path="clients" element={<Clients />} />
                <Route path="licenses" element={<Licenses />} />
                <Route path="workers" element={<Workers />} />
                <Route path="reports" element={<Reports />} />
                <Route path="expired" element={<Expired />} />
                <Route path="pending" element={<Pending />} />
                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
          </BrowserRouter>
          <Toaster position="top-left" dir="rtl" />
        </div>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
