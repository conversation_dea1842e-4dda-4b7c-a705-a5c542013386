export interface Client {
  id: string;
  name: string;
  companyName?: string;
  phone: string;
  email?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface License {
  id: string;
  clientId: string;
  type: LicenseType;
  licenseNumber: string;
  issueDate: Date;
  expiryDate: Date;
  status: LicenseStatus;
  notes?: string;
  workersCount?: number;
}

export type LicenseType =
  | "commercial" // ترخيص تجاري
  | "fire_dept" // ترخيص مطافي
  | "health" // ترخيص صحة
  | "advertisement" // ترخيص إعلان
  | "other"; // ترخيص أخرى

export type LicenseStatus =
  | "active"
  | "expired"
  | "pending_renewal"
  | "suspended";

export interface Approval {
  id: string;
  clientId: string;
  type: ApprovalType;
  approvalNumber: string;
  issueDate: Date;
  expiryDate: Date;
  status: ApprovalStatus;
  notes?: string;
}

export type ApprovalType =
  | "affairs_signature" // اعتماد توقيع شؤون
  | "traffic_signature" // اعتماد توقيع مرور
  | "customs_signature"; // اعتماد توقيع جمارك

export type ApprovalStatus = "active" | "expired" | "pending" | "rejected";

export interface RepresentativeCard {
  id: string;
  clientId: string;
  type: RepresentativeCardType;
  cardNumber: string;
  holderName: string;
  issueDate: Date;
  expiryDate: Date;
  status: CardStatus;
}

export type RepresentativeCardType =
  | "traffic_rep" // بطاقة مندوب مرور
  | "affairs_rep" // بطاقة مندوب شؤون
  | "internal_rep"; // بطاقة مندوب داخلية

export type CardStatus = "active" | "expired" | "lost" | "suspended";

export interface Worker {
  id: string;
  clientId: string;
  name: string;
  nationality: string;
  passportNumber: string;
  passportExpiryDate: Date;
  residencyExpiryDate?: Date;
  drivingLicenseNumber?: string;
  drivingLicenseExpiryDate?: Date;
  workerType: WorkerType;
  position?: string;
  isActive: boolean;
  notes?: string;
}

export type WorkerType =
  | "employee" // عامل
  | "domestic" // عاملة منزلية
  | "family_attachment"; // التحاق بعائل

export interface ExpiryAlert {
  id: string;
  itemType:
    | "license"
    | "approval"
    | "card"
    | "passport"
    | "residency"
    | "driving_license";
  itemId: string;
  clientId: string;
  clientName: string;
  itemName: string;
  expiryDate: Date;
  daysUntilExpiry: number;
  severity: "info" | "warning" | "danger";
}

export interface DashboardStats {
  totalClients: number;
  totalLicenses: number;
  totalWorkers: number;
  expiringThisMonth: number;
  expiredItems: number;
}
